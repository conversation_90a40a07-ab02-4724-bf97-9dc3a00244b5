import fs from "fs/promises";
import { app } from "electron";
import { existsSync, mkdirSync } from "fs";
import path from "path";
import { loadFfmpeg, loadImageScript } from '../main.js';
import { imageWorkerPool } from '../workers/workerPool.js';
import os from "os";
import crypto from "crypto";

// Helper function to generate thumbnail using ImageScript as fallback
async function generateImageScriptThumbnail(filePath: string, thumbnailPath: string): Promise<void> {
  try {
    console.log("Generating thumbnail using ImageScript for:", path.basename(filePath));

    // Try using worker pool first
    try {
      const workerResult = await imageWorkerPool.executeTask({
        operation: 'generateThumbnail',
        filePath: filePath,
        options: { maxHeight: 512 }
      });

      if (workerResult.success && workerResult.buffer) {
        // Convert array back to buffer and save
        const thumbnailBuffer = Buffer.from(workerResult.buffer);
        await fs.writeFile(thumbnailPath, thumbnailBuffer);
        console.log("Successfully generated thumbnail using ImageScript worker");
        return;
      } else {
        throw new Error(workerResult.error || 'Worker failed to generate thumbnail');
      }
    } catch (workerError) {
      console.warn("ImageScript worker failed, trying main thread:", workerError);

      // Fallback to main thread ImageScript
      const ImageScript = await loadImageScript();
      const imageBuffer = await fs.readFile(filePath);
      const image = await ImageScript.decode(imageBuffer);

      // Calculate dimensions to maintain aspect ratio within 512px height
      const maxHeight = 512;
      let newWidth = image.width;
      let newHeight = image.height;

      if (newHeight > maxHeight) {
        const aspectRatio = newWidth / newHeight;
        newHeight = maxHeight;
        newWidth = Math.round(maxHeight * aspectRatio);
      }

      // Resize and encode
      const resizedImage = image.resize(newWidth, newHeight);
      const jpegBuffer = await resizedImage.encodeJPEG(80); // Good quality for thumbnails

      await fs.writeFile(thumbnailPath, Buffer.from(jpegBuffer));
      console.log("Successfully generated thumbnail using ImageScript main thread");
    }
  } catch (error) {
    console.error("ImageScript thumbnail generation failed:", error);
    throw error;
  }
}



export async function generateThumbnail(
  filePath: string
): Promise<{ path: string; base64?: string; isOsThumbnail: boolean }> {
  try {
    // Validate file exists
    if (!existsSync(filePath)) {
      console.error("File does not exist:", filePath);
      throw new Error("File not found");
    }

    if (os.platform() === "linux") {
      try {
        const uri = `file://${filePath}`;
        const md5 = crypto.createHash("md5").update(uri).digest("hex");
        const thumbnailDirs = [
          path.join(os.homedir(), ".cache/thumbnails/large"),
          path.join(os.homedir(), ".cache/thumbnails/normal")
        ];

        for (const dir of thumbnailDirs) {
          const thumbnailPath = path.join(dir, `${md5}.png`);
          console.log("Checking for Linux thumbnail at:", thumbnailPath);
          if (existsSync(thumbnailPath)) {
            console.log("Found Linux thumbnail at:", thumbnailPath);
            const thumbnailBuffer = await fs.readFile(thumbnailPath);
            const base64Data = thumbnailBuffer.toString("base64");
            return {
              path: thumbnailPath,
              base64: base64Data,
              isOsThumbnail: true,
            };
          }
        }
        console.log("No Linux thumbnail found, trying fallback generation...");

        // Fallback: Generate thumbnail using appropriate method based on file type
        const thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
        if (!existsSync(thumbnailsDir)) {
          mkdirSync(thumbnailsDir, { recursive: true });
        }

        const thumbnailName = `${Buffer.from(filePath).toString("base64")}.jpg`;
        const fallbackThumbnailPath = path.join(thumbnailsDir, thumbnailName);

        // Check if we already generated this thumbnail
        if (!existsSync(fallbackThumbnailPath)) {
          const extension = path.extname(filePath).toLowerCase();
          const isVideo = [".mp4", ".mov", ".avi", ".mkv"].includes(extension);

          if (isVideo) {
            // For videos, create a placeholder since we can't use ImageScript to decode video files
            console.log("Creating placeholder thumbnail for video file");
            const ImageScript = await loadImageScript();

            const placeholderImage = new ImageScript(512, 288); // 16:9 aspect ratio
            placeholderImage.fill(0x2D3748FF); // Dark blue-gray background

            // Add a play button indicator
            const centerX = 256;
            const centerY = 144;
            placeholderImage.drawCircle(centerX, centerY, 60, 0x4A5568FF);
            placeholderImage.drawBox(centerX - 12, centerY - 12, 12, 8, 0xFFFFFFFF);
            placeholderImage.drawBox(centerX - 6, centerY - 8, 12, 16, 0xFFFFFFFF);

            const jpegBuffer = await placeholderImage.encodeJPEG(80);
            await fs.writeFile(fallbackThumbnailPath, Buffer.from(jpegBuffer));
            console.log("Created placeholder thumbnail for video");
          } else {
            // For images, use ImageScript to generate proper thumbnail
            await generateImageScriptThumbnail(filePath, fallbackThumbnailPath);
          }
        }

        if (existsSync(fallbackThumbnailPath)) {
          const thumbnailBuffer = await fs.readFile(fallbackThumbnailPath);
          const base64Data = thumbnailBuffer.toString("base64");
          console.log("Successfully generated Linux fallback thumbnail");
          return {
            path: fallbackThumbnailPath,
            base64: base64Data,
            isOsThumbnail: false,
          };
        }

        throw new Error("Failed to generate fallback thumbnail");
      } catch (error) {
        console.error("Linux thumbnail generation failed:", error);
        throw error;
      }
    }

    const thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
    if (!existsSync(thumbnailsDir)) {
      mkdirSync(thumbnailsDir, { recursive: true });
    }

    const thumbnailName = `${Buffer.from(filePath).toString("base64")}.jpg`;
    const thumbnailPath = path.join(thumbnailsDir, thumbnailName);

    const extension = path.extname(filePath).toLowerCase();
    const isVideo = [".mp4", ".mov", ".avi", ".mkv"].includes(extension);

    if (isVideo) {
      try {
        if (!existsSync(thumbnailPath)) {
          try {
            // Try FFmpeg first
            console.log("Attempting video thumbnail generation with FFmpeg for:", path.basename(filePath));
            const ffmpeg = await loadFfmpeg();

            await new Promise<void>((resolve, reject) => {
              ffmpeg(filePath)
                .takeScreenshots({
                  timestamps: ["00:00:01"],
                  filename: thumbnailName,
                  folder: thumbnailsDir,
                  size: "?x512",
                })
                .on("end", () => resolve())
                .on("error", (err) => {
                  console.error("FFmpeg error:", err);
                  reject(err);
                });
            });
          } catch (ffmpegError) {
            console.warn("FFmpeg failed, creating placeholder thumbnail for video:", ffmpegError);

            // For videos, we can't use ImageScript to decode the video file directly
            // So we skip the ImageScript attempt and go straight to placeholder creation
            console.log("Creating placeholder thumbnail for video");
            const ImageScript = await loadImageScript();

            // Create a simple colored rectangle as placeholder with a play button indicator
            const placeholderImage = new ImageScript(512, 288); // 16:9 aspect ratio
            placeholderImage.fill(0x2D3748FF); // Dark blue-gray background

            // Add a play button indicator in the center
            const centerX = 256;
            const centerY = 144;
            const playButtonSize = 60;

            // Draw play button background (circle)
            placeholderImage.drawCircle(centerX, centerY, playButtonSize, 0x4A5568FF);

            // Draw play triangle (approximate)
            const triangleSize = 25;
            placeholderImage.drawBox(centerX - triangleSize/2, centerY - triangleSize/2, triangleSize, triangleSize/3, 0xFFFFFFFF);
            placeholderImage.drawBox(centerX - triangleSize/4, centerY - triangleSize/3, triangleSize/2, triangleSize*2/3, 0xFFFFFFFF);

            const jpegBuffer = await placeholderImage.encodeJPEG(80);
            await fs.writeFile(thumbnailPath, Buffer.from(jpegBuffer));
            console.log("Created placeholder thumbnail for video");
          }
        }

        // Verify thumbnail was created
        if (!existsSync(thumbnailPath)) {
          throw new Error("All video thumbnail generation methods failed");
        }

        const thumbnailBuffer = await fs.readFile(thumbnailPath);
        const base64Data = thumbnailBuffer.toString("base64");

        return {
          path: thumbnailPath,
          base64: base64Data,
          isOsThumbnail: false,
        };
      } catch (error) {
        console.error("Video thumbnail generation error:", error);
        throw error;
      }
    }

    // Handle images using ImageScript for proper thumbnail generation
    try {
      if (!existsSync(thumbnailPath)) {
        try {
          // Use ImageScript to generate a proper thumbnail
          console.log("Generating image thumbnail using ImageScript for:", path.basename(filePath));
          await generateImageScriptThumbnail(filePath, thumbnailPath);
        } catch (imageScriptError) {
          console.warn("ImageScript failed, using file copy fallback:", imageScriptError);

          // Fallback: Copy the original file
          const originalBuffer = await fs.readFile(filePath);
          await fs.writeFile(thumbnailPath, originalBuffer);
          console.log("Used file copy as thumbnail fallback");
        }
      }

      // Verify thumbnail was created
      if (!existsSync(thumbnailPath)) {
        throw new Error("Thumbnail generation failed");
      }

      // Read the thumbnail for base64 encoding
      const thumbnailBuffer = await fs.readFile(thumbnailPath);
      const base64Data = thumbnailBuffer.toString("base64");

      return {
        path: thumbnailPath,
        base64: base64Data,
        isOsThumbnail: false
      };
    } catch (error) {
      console.error("Image thumbnail generation error:", error);
      throw error;
    }
  } catch (error) {
    console.error("Thumbnail generation failed for:", filePath, error);
    throw error;
  }
}
