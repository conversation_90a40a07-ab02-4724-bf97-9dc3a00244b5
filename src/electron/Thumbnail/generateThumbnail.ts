import fs from "fs/promises";
import { app } from "electron";
import { existsSync, mkdirSync } from "fs";
import path from "path";
import { loadFfmpeg, loadImageScript } from '../main.js';
import { imageWorkerPool } from '../workers/workerPool.js';
import os from "os";
import crypto from "crypto";

// Helper function to generate thumbnail using ImageScript as fallback
async function generateImageScriptThumbnail(filePath: string, thumbnailPath: string): Promise<void> {
  try {
    console.log("Generating thumbnail using ImageScript for:", path.basename(filePath));

    // Try using worker pool first
    try {
      const workerResult = await imageWorkerPool.executeTask({
        operation: 'generateThumbnail',
        filePath: filePath,
        options: { maxHeight: 512 }
      });

      if (workerResult.success && workerResult.buffer) {
        // Convert array back to buffer and save
        const thumbnailBuffer = Buffer.from(workerResult.buffer);
        await fs.writeFile(thumbnailPath, thumbnailBuffer);
        console.log("Successfully generated thumbnail using ImageScript worker");
        return;
      } else {
        throw new Error(workerResult.error || 'Worker failed to generate thumbnail');
      }
    } catch (workerError) {
      console.warn("ImageScript worker failed, trying main thread:", workerError);

      // Fallback to main thread ImageScript
      const ImageScript = await loadImageScript();
      const imageBuffer = await fs.readFile(filePath);
      const image = await ImageScript.decode(imageBuffer);

      // Calculate dimensions to maintain aspect ratio within 512px height
      const maxHeight = 512;
      let newWidth = image.width;
      let newHeight = image.height;

      if (newHeight > maxHeight) {
        const aspectRatio = newWidth / newHeight;
        newHeight = maxHeight;
        newWidth = Math.round(maxHeight * aspectRatio);
      }

      // Resize and encode
      const resizedImage = image.resize(newWidth, newHeight);
      const jpegBuffer = await resizedImage.encodeJPEG(80); // Good quality for thumbnails

      await fs.writeFile(thumbnailPath, Buffer.from(jpegBuffer));
      console.log("Successfully generated thumbnail using ImageScript main thread");
    }
  } catch (error) {
    console.error("ImageScript thumbnail generation failed:", error);
    throw error;
  }
}



export async function generateThumbnail(
  filePath: string
): Promise<{ path: string; base64?: string; isOsThumbnail: boolean }> {
  try {
    // Validate file exists
    if (!existsSync(filePath)) {
      console.error("File does not exist:", filePath);
      throw new Error("File not found");
    }

    if (os.platform() === "linux") {
      try {
        const uri = `file://${filePath}`;
        const md5 = crypto.createHash("md5").update(uri).digest("hex");
        const thumbnailDirs = [
          path.join(os.homedir(), ".cache/thumbnails/large"),
          path.join(os.homedir(), ".cache/thumbnails/normal")
        ];

        for (const dir of thumbnailDirs) {
          const thumbnailPath = path.join(dir, `${md5}.png`);
          console.log("Checking for Linux thumbnail at:", thumbnailPath);
          if (existsSync(thumbnailPath)) {
            console.log("Found Linux thumbnail at:", thumbnailPath);
            const thumbnailBuffer = await fs.readFile(thumbnailPath);
            const base64Data = thumbnailBuffer.toString("base64");
            return {
              path: thumbnailPath,
              base64: base64Data,
              isOsThumbnail: true,
            };
          }
        }
        console.log("No Linux thumbnail found, trying ImageScript fallback...");

        // Fallback: Generate thumbnail using ImageScript
        const thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
        if (!existsSync(thumbnailsDir)) {
          mkdirSync(thumbnailsDir, { recursive: true });
        }

        const thumbnailName = `${Buffer.from(filePath).toString("base64")}.jpg`;
        const fallbackThumbnailPath = path.join(thumbnailsDir, thumbnailName);

        // Check if we already generated this thumbnail
        if (!existsSync(fallbackThumbnailPath)) {
          await generateImageScriptThumbnail(filePath, fallbackThumbnailPath);
        }

        if (existsSync(fallbackThumbnailPath)) {
          const thumbnailBuffer = await fs.readFile(fallbackThumbnailPath);
          const base64Data = thumbnailBuffer.toString("base64");
          console.log("Successfully generated Linux fallback thumbnail using ImageScript");
          return {
            path: fallbackThumbnailPath,
            base64: base64Data,
            isOsThumbnail: false,
          };
        }

        throw new Error("Failed to generate fallback thumbnail");
      } catch (error) {
        console.error("Linux thumbnail generation failed:", error);
        throw error;
      }
    }

    const thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
    if (!existsSync(thumbnailsDir)) {
      mkdirSync(thumbnailsDir, { recursive: true });
    }

    const thumbnailName = `${Buffer.from(filePath).toString("base64")}.jpg`;
    const thumbnailPath = path.join(thumbnailsDir, thumbnailName);

    const extension = path.extname(filePath).toLowerCase();
    const isVideo = [".mp4", ".mov", ".avi", ".mkv"].includes(extension);

    if (isVideo) {
      try {
        if (!existsSync(thumbnailPath)) {
          try {
            // Try FFmpeg first
            console.log("Attempting video thumbnail generation with FFmpeg for:", path.basename(filePath));
            const ffmpeg = await loadFfmpeg();

            await new Promise<void>((resolve, reject) => {
              ffmpeg(filePath)
                .takeScreenshots({
                  timestamps: ["00:00:01"],
                  filename: thumbnailName,
                  folder: thumbnailsDir,
                  size: "?x512",
                })
                .on("end", () => resolve())
                .on("error", (err) => {
                  console.error("FFmpeg error:", err);
                  reject(err);
                });
            });
          } catch (ffmpegError) {
            console.warn("FFmpeg failed, trying ImageScript fallback for video:", ffmpegError);

            // Fallback: Try to extract a frame using ImageScript if the video has a poster/thumbnail
            // This is a limited fallback - ImageScript can't extract video frames directly
            // but we can try to read the file as if it might have embedded thumbnail data
            try {
              await generateImageScriptThumbnail(filePath, thumbnailPath);
              console.log("Generated video thumbnail using ImageScript fallback");
            } catch (imageScriptError) {
              console.error("ImageScript fallback also failed for video:", imageScriptError);

              // Final fallback: Create a placeholder thumbnail
              console.log("Creating placeholder thumbnail for video");
              const ImageScript = await loadImageScript();

              // Create a simple colored rectangle as placeholder
              const placeholderImage = new ImageScript(512, 288); // 16:9 aspect ratio
              placeholderImage.fill(0x333333FF); // Dark gray background

              // Add some basic "video" indicator (we could make this more sophisticated)
              const jpegBuffer = await placeholderImage.encodeJPEG(80);
              await fs.writeFile(thumbnailPath, Buffer.from(jpegBuffer));
              console.log("Created placeholder thumbnail for video");
            }
          }
        }

        // Verify thumbnail was created
        if (!existsSync(thumbnailPath)) {
          throw new Error("All video thumbnail generation methods failed");
        }

        const thumbnailBuffer = await fs.readFile(thumbnailPath);
        const base64Data = thumbnailBuffer.toString("base64");

        return {
          path: thumbnailPath,
          base64: base64Data,
          isOsThumbnail: false,
        };
      } catch (error) {
        console.error("Video thumbnail generation error:", error);
        throw error;
      }
    }

    // Handle images using ImageScript for proper thumbnail generation
    try {
      if (!existsSync(thumbnailPath)) {
        try {
          // Use ImageScript to generate a proper thumbnail
          console.log("Generating image thumbnail using ImageScript for:", path.basename(filePath));
          await generateImageScriptThumbnail(filePath, thumbnailPath);
        } catch (imageScriptError) {
          console.warn("ImageScript failed, using file copy fallback:", imageScriptError);

          // Fallback: Copy the original file
          const originalBuffer = await fs.readFile(filePath);
          await fs.writeFile(thumbnailPath, originalBuffer);
          console.log("Used file copy as thumbnail fallback");
        }
      }

      // Verify thumbnail was created
      if (!existsSync(thumbnailPath)) {
        throw new Error("Thumbnail generation failed");
      }

      // Read the thumbnail for base64 encoding
      const thumbnailBuffer = await fs.readFile(thumbnailPath);
      const base64Data = thumbnailBuffer.toString("base64");

      return {
        path: thumbnailPath,
        base64: base64Data,
        isOsThumbnail: false
      };
    } catch (error) {
      console.error("Image thumbnail generation error:", error);
      throw error;
    }
  } catch (error) {
    console.error("Thumbnail generation failed for:", filePath, error);
    throw error;
  }
}
