import fs from "fs/promises";
import { app } from "electron";
import { existsSync, mkdirSync } from "fs";
import path from "path";
import { loadFfmpeg, loadImageScript } from '../main.js';
import { imageWorkerPool } from '../workers/workerPool.js';
import os from "os";
import crypto from "crypto";



export async function generateThumbnail(
  filePath: string
): Promise<{ path: string; base64?: string; isOsThumbnail: boolean }> {
  try {
    // Validate file exists
    if (!existsSync(filePath)) {
      console.error("File does not exist:", filePath);
      throw new Error("File not found");
    }

    if (os.platform() === "linux") {
      try {
        const uri = `file://${filePath}`;
        const md5 = crypto.createHash("md5").update(uri).digest("hex");
        const thumbnailDirs = [
          path.join(os.homedir(), ".cache/thumbnails/large"),
          path.join(os.homedir(), ".cache/thumbnails/normal")
        ];

        for (const dir of thumbnailDirs) {
          const thumbnailPath = path.join(dir, `${md5}.png`);
          console.log("Checking for Linux thumbnail at:", thumbnailPath);
          if (existsSync(thumbnailPath)) {
            console.log("Found Linux thumbnail at:", thumbnailPath);
            const thumbnailBuffer = await fs.readFile(thumbnailPath);
            const base64Data = thumbnailBuffer.toString("base64");
            return {
              path: thumbnailPath,
              base64: base64Data,
              isOsThumbnail: true,
            };
          }
        }
        console.log("No Linux thumbnail found.");
        throw new Error("No Linux thumbnail found");
      } catch (error) {
        console.error("Linux thumbnail check failed:", error);
        throw error;
      }
    }

    const thumbnailsDir = path.join(app.getPath("userData"), "thumbnails");
    if (!existsSync(thumbnailsDir)) {
      mkdirSync(thumbnailsDir, { recursive: true });
    }

    const thumbnailName = `${Buffer.from(filePath).toString("base64")}.jpg`;
    const thumbnailPath = path.join(thumbnailsDir, thumbnailName);

    const extension = path.extname(filePath).toLowerCase();
    const isVideo = [".mp4", ".mov", ".avi", ".mkv"].includes(extension);

    if (isVideo) {
      try {
        if (!existsSync(thumbnailPath)) {
          // Dynamically load ffmpeg
          const ffmpeg = await loadFfmpeg();

          await new Promise<void>((resolve, reject) => {
            ffmpeg(filePath)
              .takeScreenshots({
                timestamps: ["00:00:01"],
                filename: thumbnailName,
                folder: thumbnailsDir,
                size: "?x512",
              })
              .on("end", () => resolve())
              .on("error", (err) => {
                console.error("FFmpeg error:", err);
                reject(err);
              });
          });
        }

        // Verify thumbnail was created
        if (!existsSync(thumbnailPath)) {
          throw new Error("Thumbnail generation failed");
        }

        const thumbnailBuffer = await fs.readFile(thumbnailPath);
        const base64Data = thumbnailBuffer.toString("base64");

        return {
          path: thumbnailPath,
          base64: base64Data,
          isOsThumbnail: false,
        };
      } catch (error) {
        console.error("Video thumbnail generation error:", error);
        throw error;
      }
    }

    // Handle images by copying the file
    try {
      if (!existsSync(thumbnailPath)) {
        try {
          // Read the original file
          const originalBuffer = await fs.readFile(filePath);

          // Write it to the thumbnail location
          await fs.writeFile(thumbnailPath, originalBuffer);
          console.log("Used file copy as thumbnail fallback");
        } catch (fallbackError) {
          console.error("All thumbnail generation methods failed:", fallbackError);
          throw fallbackError;
        }
      }

      // Verify thumbnail was created
      if (!existsSync(thumbnailPath)) {
        throw new Error("Thumbnail generation failed");
      }

      // Read the thumbnail for base64 encoding
      const thumbnailBuffer = await fs.readFile(thumbnailPath);
      const base64Data = thumbnailBuffer.toString("base64");

      return {
        path: thumbnailPath,
        base64: base64Data,
        isOsThumbnail: false
      };
    } catch (error) {
      console.error("Image thumbnail generation error:", error);
      throw error;
    }
  } catch (error) {
    console.error("Thumbnail generation failed for:", filePath, error);
    throw error;
  }
}
