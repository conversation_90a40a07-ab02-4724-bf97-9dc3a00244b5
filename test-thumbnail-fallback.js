#!/usr/bin/env node

/**
 * Test script to verify thumbnail generation fallback functionality
 * This script tests the ImageScript fallback when OS thumbnails are not available
 */

import { generateThumbnail } from './dist-electron/Thumbnail/generateThumbnail.js';
import fs from 'fs';
import path from 'path';

async function testThumbnailFallback() {
  console.log('🧪 Testing thumbnail generation fallback functionality...\n');

  // Test cases - you can add your own test files here
  const testCases = [
    // Add paths to test files here - these should be files that don't have OS thumbnails
    // Example: './test-files/sample-video.mp4',
    // Example: './test-files/sample-image.jpg',
  ];

  // If no test cases provided, create a simple test image using ImageScript
  if (testCases.length === 0) {
    console.log('📝 No test files provided. Creating a test image using ImageScript...');
    
    try {
      // Import ImageScript to create a test image
      const { Image } = await import('imagescript');
      
      // Create a simple test image
      const testImage = new Image(400, 300);
      testImage.fill(0x4A90E2FF); // Blue background
      
      // Draw a simple pattern
      testImage.drawBox(50, 50, 300, 200, 0xFFFFFFFF); // White rectangle
      testImage.drawCircle(200, 150, 50, 0xFF6B6BFF); // Red circle
      
      // Save the test image
      const testImagePath = './test-image.png';
      const pngBuffer = await testImage.encode();
      fs.writeFileSync(testImagePath, Buffer.from(pngBuffer));
      
      console.log(`✅ Created test image: ${testImagePath}`);
      testCases.push(testImagePath);
    } catch (error) {
      console.error('❌ Failed to create test image:', error.message);
      return;
    }
  }

  // Test each file
  for (const filePath of testCases) {
    console.log(`\n🔍 Testing: ${path.basename(filePath)}`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      continue;
    }

    try {
      const startTime = Date.now();
      const result = await generateThumbnail(filePath);
      const endTime = Date.now();
      
      console.log(`✅ Thumbnail generated successfully!`);
      console.log(`   📁 Path: ${result.path}`);
      console.log(`   🖼️  OS Thumbnail: ${result.isOsThumbnail ? 'Yes' : 'No (using fallback)'}`);
      console.log(`   ⏱️  Time: ${endTime - startTime}ms`);
      console.log(`   📊 Base64 size: ${result.base64 ? result.base64.length : 0} characters`);
      
      // Verify the thumbnail file exists
      if (fs.existsSync(result.path)) {
        const stats = fs.statSync(result.path);
        console.log(`   📏 File size: ${stats.size} bytes`);
      } else {
        console.log(`   ❌ Thumbnail file not found at: ${result.path}`);
      }
      
    } catch (error) {
      console.log(`❌ Failed to generate thumbnail: ${error.message}`);
      console.log(`   Stack: ${error.stack}`);
    }
  }

  // Clean up test image if we created one
  if (fs.existsSync('./test-image.png')) {
    fs.unlinkSync('./test-image.png');
    console.log('\n🧹 Cleaned up test image');
  }

  console.log('\n🎉 Test completed!');
}

// Run the test
testThumbnailFallback().catch(error => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
